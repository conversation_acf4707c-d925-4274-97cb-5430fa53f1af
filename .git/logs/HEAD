0000000000000000000000000000000000000000 85fee7406c6733f0056aaa93aaab39db42dc2956 Rogier Wachters <<EMAIL>> 1748444829 +0200	commit (initial): Add initial project setup, docs, and tech stack.
85fee7406c6733f0056aaa93aaab39db42dc2956 8523f0e4b5c9cbe8d622bde3e230a771eec74d12 Rogier Wachters <<EMAIL>> 1748449612 +0200	commit (amend): Add initial project setup, docs, and tech stack.
8523f0e4b5c9cbe8d622bde3e230a771eec74d12 1702c5f26885df65aa8242c290e1b6ea9451cb7c Rogier Wachters <<EMAIL>> 1748450333 +0200	commit (amend): Add initial project setup, docs, and tech stack.
1702c5f26885df65aa8242c290e1b6ea9451cb7c a42d83098a76655fed019dba7ba66bd845e7fca9 Rogier Wachters <<EMAIL>> 1748576284 +0200	commit (amend): Add initial project setup, docs, and tech stack.
a42d83098a76655fed019dba7ba66bd845e7fca9 e132096039de00524a67a212ca83f1b02eaef373 Rogier Wachters <<EMAIL>> 1748576422 +0200	commit (amend): Add initial project setup, docs, and tech stack.
e132096039de00524a67a212ca83f1b02eaef373 e132096039de00524a67a212ca83f1b02eaef373 Rogier Wachters <<EMAIL>> 1748920862 +0200	checkout: moving from master to infra/42-initial-multimodule-gradle
e132096039de00524a67a212ca83f1b02eaef373 f80e57ce292a6af2d0ac04fcbc393f085fd431c6 Rogier Wachters <<EMAIL>> 1748921602 +0200	commit: Refactor project structure and enhance build scripts.
f80e57ce292a6af2d0ac04fcbc393f085fd431c6 257900812cb72bdd92422767e3dafae46a8f6070 Rogier Wachters <<EMAIL>> 1748921998 +0200	commit: Add comprehensive documentation for project summary and user stories.
257900812cb72bdd92422767e3dafae46a8f6070 c595306afc3140bc1dc7c26bd7ee9d618a910504 Rogier Wachters <<EMAIL>> 1748922480 +0200	checkout: moving from infra/42-initial-multimodule-gradle to master
c595306afc3140bc1dc7c26bd7ee9d618a910504 c595306afc3140bc1dc7c26bd7ee9d618a910504 Rogier Wachters <<EMAIL>> 1748923410 +0200	checkout: moving from master to feat/44-common-data-models
c595306afc3140bc1dc7c26bd7ee9d618a910504 26e9f46e5ba2a3490d319bf2e0bba98955600037 Rogier Wachters <<EMAIL>> 1748926251 +0200	commit: Add core shared data models for chat functionality
26e9f46e5ba2a3490d319bf2e0bba98955600037 c204ff2eed49796c1d86382fd30be05d2094bba3 Rogier Wachters <<EMAIL>> 1748926291 +0200	commit: Add request and response models for LLM configurations and chat session management
c204ff2eed49796c1d86382fd30be05d2094bba3 12953ebfe295dd42ab13cd356730d289792c27a2 Rogier Wachters <<EMAIL>> 1748927113 +0200	checkout: moving from feat/44-common-data-models to master
12953ebfe295dd42ab13cd356730d289792c27a2 12953ebfe295dd42ab13cd356730d289792c27a2 Rogier Wachters <<EMAIL>> 1748986144 +0200	checkout: moving from master to infra/46-server-db-setup-tx
12953ebfe295dd42ab13cd356730d289792c27a2 f44172b201b8223b5364c0803011d6e8eaca0210 Rogier Wachters <<EMAIL>> 1748992653 +0200	commit: Add server-side data models for Exposed ORM integration
f44172b201b8223b5364c0803011d6e8eaca0210 253b78b24f5c37e5e14da4e1e36eda9df9f39994 Rogier Wachters <<EMAIL>> 1748992693 +0200	commit: Implement coroutine-safe transaction management utilities
253b78b24f5c37e5e14da4e1e36eda9df9f39994 702e796c9c0918a5f6cd33308edd71e273505f6c Rogier Wachters <<EMAIL>> 1748992742 +0200	commit: Add database initialization and schema management for server data layer
702e796c9c0918a5f6cd33308edd71e273505f6c 99096bdbed24b53a4c7f9d14e77e7839512e1c42 Rogier Wachters <<EMAIL>> 1748992776 +0200	commit: Add project development guidelines documentation
99096bdbed24b53a4c7f9d14e77e7839512e1c42 2a141e01869e81442d8477a9010d44b5cc3444aa Rogier Wachters <<EMAIL>> 1748993542 +0200	checkout: moving from infra/46-server-db-setup-tx to master
2a141e01869e81442d8477a9010d44b5cc3444aa 2a141e01869e81442d8477a9010d44b5cc3444aa Rogier Wachters <<EMAIL>> 1748994141 +0200	checkout: moving from master to 50-e5s1-secure-credential-manager-implementation
2a141e01869e81442d8477a9010d44b5cc3444aa 3698f6824f8d3f049da1c4b96de379db5654db12 Rogier Wachters <<EMAIL>> 1749160636 +0200	commit: Add `ApiSecretsTable` and `ApiSecretEntity` for managing encrypted API secrets
3698f6824f8d3f049da1c4b96de379db5654db12 9379f3ba6cf6e5cf604fbd2811b659ef1218d6e2 Rogier Wachters <<EMAIL>> 1749160696 +0200	commit: Add `ApiSecretDao` and its Exposed implementation for managing encrypted API secrets
9379f3ba6cf6e5cf604fbd2811b659ef1218d6e2 d96c4441ca480f2cc4289511fc74cebd1c20f5c5 Rogier Wachters <<EMAIL>> 1749160977 +0200	commit: Add envelope encryption support with AES-based implementation
d96c4441ca480f2cc4289511fc74cebd1c20f5c5 660fe659643ae7647acab61f05d2ffa8372290a9 Rogier Wachters <<EMAIL>> 1749161109 +0200	commit: Add `EncryptedSecret` and `EncryptionService` for envelope encryption support
660fe659643ae7647acab61f05d2ffa8372290a9 9dc21d9ce509c49cf3e0bb795c6be9a446920e5b Rogier Wachters <<EMAIL>> 1749161189 +0200	commit: Add `CredentialManager` and `DbEncryptedCredentialManager` for secure credential storage
9dc21d9ce509c49cf3e0bb795c6be9a446920e5b a91b63d15593915c4a07b4eb94d957a357494ba8 Rogier Wachters <<EMAIL>> 1749161364 +0200	commit: Introduce framework-agnostic `DIContainer` interface and Koin-based implementation
a91b63d15593915c4a07b4eb94d957a357494ba8 6e12255cd7823722a88704a15f001cb66a5d8c19 Rogier Wachters <<EMAIL>> 1749161484 +0200	commit: Add Koin modules for configuration, database, DAOs, and miscellaneous services
6e12255cd7823722a88704a15f001cb66a5d8c19 27ff2181cf6953b809619ba2ece272776b64e510 Rogier Wachters <<EMAIL>> ********** +0200	commit: Add test utility framework for database operations and test data management
27ff2181cf6953b809619ba2ece272776b64e510 73972fc74c90ac25a3ca8a82f6685a3289da8bb5 Rogier Wachters <<EMAIL>> ********** +0200	commit: Add Koin modules and DI container for test utilities
73972fc74c90ac25a3ca8a82f6685a3289da8bb5 eb1863f439848882cc428958eb8a950f4bdc80da Rogier Wachters <<EMAIL>> ********** +0200	commit: Add unit tests for `AESCryptoProvider` encryption and key management
eb1863f439848882cc428958eb8a950f4bdc80da bbfb109f892f21e062e70f808593ce5b24758a92 Rogier Wachters <<EMAIL>> ********** +0200	commit: Add unit tests for `EncryptionService`
bbfb109f892f21e062e70f808593ce5b24758a92 3fb8772486cc2c2c0fbf6c1a79bbf719f49065d7 Rogier Wachters <<EMAIL>> ********** +0200	commit: Add unit tests for `ApiSecretDaoExposed`
3fb8772486cc2c2c0fbf6c1a79bbf719f49065d7 f86eee840fdf67b7e7e2edf757068e795146fdd2 Rogier Wachters <<EMAIL>> ********** +0200	commit: Add unit tests for `DbEncryptedCredentialManager`
f86eee840fdf67b7e7e2edf757068e795146fdd2 33f0a9d5f5801743e71fd29e327ddb8df27b309e Rogier Wachters <<EMAIL>> ********** +0200	commit: Update docs
33f0a9d5f5801743e71fd29e327ddb8df27b309e b185d90e773fc7fd410df024b6e242498547aad4 Rogier Wachters <<EMAIL>> ********** +0200	checkout: moving from 50-e5s1-secure-credential-manager-implementation to master
b185d90e773fc7fd410df024b6e242498547aad4 b185d90e773fc7fd410df024b6e242498547aad4 Rogier Wachters <<EMAIL>> 1749167480 +0200	checkout: moving from master to 52-server-backend---core-daos
